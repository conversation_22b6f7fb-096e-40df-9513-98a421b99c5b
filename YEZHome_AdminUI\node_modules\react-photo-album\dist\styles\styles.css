.react-photo-album{display:flex}.react-photo-album,.react-photo-album *{box-sizing:border-box}.react-photo-album--track{align-items:flex-start;display:flex}.react-photo-album--photo{padding:calc(var(--react-photo-album--padding)*1px);position:relative}.react-photo-album--image{aspect-ratio:var(--react-photo-album--photo-width)/var(--react-photo-album--photo-height);display:block;height:auto;width:100%}.react-photo-album--button{background:initial;border:initial;cursor:pointer}.react-photo-album--rows{flex-direction:column;row-gap:calc(var(--react-photo-album--spacing)*1px)}.react-photo-album--rows .react-photo-album--track{justify-content:space-between;--react-photo-album--track-gaps:calc(var(--react-photo-album--spacing)*(var(--react-photo-album--track-size) - 1) + var(--react-photo-album--padding)*2*var(--react-photo-album--track-size))}.react-photo-album--rows .react-photo-album--photo{width:calc((100% - var(--react-photo-album--track-gaps)*1px)/(var(--react-photo-album--container-width) - var(--react-photo-album--track-gaps))*var(--react-photo-album--photo-width) + 2px*var(--react-photo-album--padding))}.react-photo-album--columns{justify-content:space-between}.react-photo-album--columns .react-photo-album--track{flex-direction:column;justify-content:space-between;row-gap:calc(var(--react-photo-album--spacing)*1px);width:calc((100% - 1px*(var(--react-photo-album--columns) - 1)*var(--react-photo-album--spacing) - 2px*var(--react-photo-album--columns)*var(--react-photo-album--padding) - var(--react-photo-album--adjusted-gaps)*1px)*var(--react-photo-album--column-ratio)/var(--react-photo-album--total-ratio) + 2px*var(--react-photo-album--padding))}.react-photo-album--columns .react-photo-album--photo{width:100%}.react-photo-album--masonry{justify-content:space-between}.react-photo-album--masonry .react-photo-album--track{flex-direction:column;justify-content:flex-start;row-gap:calc(var(--react-photo-album--spacing)*1px);width:calc((100% - var(--react-photo-album--spacing)*1px*(var(--react-photo-album--columns) - 1))/var(--react-photo-album--columns))}.react-photo-album--masonry .react-photo-album--photo{width:100%}