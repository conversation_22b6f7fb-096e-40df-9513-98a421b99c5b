import * as react_jsx_runtime from 'react/jsx-runtime';
import React from 'react';
import { Photo, ClickHandlerProps, CommonPhotoAlbumProps } from '../types.js';

/** InfiniteScroll component props. */
type InfiniteScrollProps<TPhoto extends Photo = Photo> = {
    /** Photo fetcher. Resolve promise with `null` to indicate end of stream. */
    fetch: (index: number) => Promise<TPhoto[] | null>;
    /** Initial photos (optional). */
    photos?: TPhoto[];
    /** Click handler */
    onClick?: ({ photos, photo, index, event }: ClickHandlerProps<TPhoto> & {
        photos: TPhoto[];
    }) => void;
    /** Retry attempts. */
    retries?: number;
    /** Use a single photo album component (masonry layout). */
    singleton?: boolean;
    /** Markup to display when an error occurred. */
    error?: React.ReactNode;
    /** Markup to display while fetching additional photos. */
    loading?: React.ReactNode;
    /** Markup to display when no more photos are available. */
    finished?: React.ReactNode;
    /** Fetcher `IntersectionObserver` root margin setting. Default: `800px` */
    fetchRootMargin?: string;
    /** Offscreen `IntersectionObserver` root margin setting. Default: `2000px` */
    offscreenRootMargin?: string;
    /** Photo album component. Must be the only child. */
    children: React.ReactElement<Pick<CommonPhotoAlbumProps<TPhoto>, "photos" | "render" | "onClick">>;
};
/** InfiniteScroll component. */
declare function InfiniteScroll<TPhoto extends Photo>({ photos: initialPhotos, onClick, fetch, retries, singleton, error, loading, finished, children, fetchRootMargin, offscreenRootMargin, }: InfiniteScrollProps<TPhoto>): react_jsx_runtime.JSX.Element;

export { InfiniteScroll as default };
export type { InfiniteScrollProps };
