import { jsxs, Fragment, jsx } from "react/jsx-runtime";
import { clsx } from "../utils/index.js";
import StaticPhotoAlbum from "../static/index.js";
import computeRowsLayout from "../layouts/rows.js";
import computeColumnsLayout from "../layouts/columns.js";
import computeMasonryLayout from "../layouts/masonry.js";
import resolveRowsProps from "../client/rowsProps.js";
import resolveColumnsProps from "../client/columnsProps.js";
import resolveMasonryProps from "../client/masonryProps.js";
import { useBreakpoints, StyledBreakpoints } from "../ssr/breakpoints.js";
function ServerPhotoAlbum({
  layout,
  unstyled,
  classNames,
  breakpoints: breakpointsProp,
  ...props
}) {
  const { photos } = props;
  const { breakpoints, containerClass, breakpointClass } = useBreakpoints("server", breakpointsProp);
  if (!Array.isArray(photos) || !Array.isArray(breakpoints) || breakpoints.length === 0) return null;
  const computeModel = (breakpoint) => {
    if (layout === "rows") {
      const { spacing, padding, targetRowHeight, minPhotos, maxPhotos, ...rest } = resolveRowsProps(breakpoint, props);
      if (spacing !== void 0 && padding !== void 0 && targetRowHeight !== void 0) {
        return {
          ...rest,
          model: computeRowsLayout(photos, spacing, padding, breakpoint, targetRowHeight, minPhotos, maxPhotos)
        };
      }
    }
    if (layout === "columns") {
      const { spacing, padding, columns, ...rest } = resolveColumnsProps(breakpoint, props);
      if (spacing !== void 0 && padding !== void 0 && columns !== void 0) {
        return {
          ...rest,
          model: computeColumnsLayout(photos, spacing, padding, breakpoint, columns)
        };
      }
    }
    if (layout === "masonry") {
      const { spacing, padding, columns, ...rest } = resolveMasonryProps(breakpoint, props);
      if (spacing !== void 0 && padding !== void 0 && columns !== void 0) {
        return {
          ...rest,
          model: computeMasonryLayout(photos, spacing, padding, breakpoint, columns)
        };
      }
    }
    return null;
  };
  return jsxs(Fragment, { children: [
    !unstyled && jsx(
      StyledBreakpoints,
      {
        breakpoints,
        containerClass,
        breakpointClass
      }
    ),
    jsx("div", { className: clsx(containerClass, classNames?.container), children: breakpoints.map((breakpoint) => jsx("div", { className: clsx(breakpointClass(breakpoint), classNames?.breakpoints?.[breakpoint]), children: jsx(StaticPhotoAlbum, { layout, ...computeModel(breakpoint) }) }, breakpoint)) })
  ] });
}
export {
  ServerPhotoAlbum as default
};
