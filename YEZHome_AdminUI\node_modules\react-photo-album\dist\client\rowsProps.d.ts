import { Photo, RowsPhotoAlbumProps, Render, ComponentsProps, ResponsiveSizes, ClickHandler } from '../types.js';
import 'react';

declare function resolveRowsProps<TPhoto extends Photo>(containerWidth: number | undefined, { photos, targetRowHeight, rowConstraints, ...rest }: RowsPhotoAlbumProps<TPhoto>): {
    targetRowHeight: number | undefined;
    render: Render<TPhoto> | undefined;
    spacing: number | undefined;
    padding: number | undefined;
    minPhotos: number | undefined;
    maxPhotos: number | undefined;
    componentsProps: ComponentsProps<TPhoto>;
    sizes?: ResponsiveSizes;
    breakpoints?: number[];
    defaultContainerWidth?: number;
    onClick?: ClickHandler<TPhoto> | undefined;
    skeleton?: React.ReactNode;
};

export { resolveRowsProps as default };
