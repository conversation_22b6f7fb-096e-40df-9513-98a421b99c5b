import axiosInstance from './axios-config';
import type {
  PropertyCountStats,
  PropertyDto,
  PagedResultDto,
  PropertySearchParams,
  UpdateStatusDto,
  BulkPropertyIdsDto,
  BulkUpdateStatusDto,
  PropertyHistoryDto
} from '@/lib/types/property';

class PropertyService {
  /**
   * Get property count statistics by status
   */
  async getPropertyCountByStatus(): Promise<PropertyCountStats> {
    const response = await axiosInstance.get<PropertyCountStats>('Property/count-by-status');
    return response.data;
  }

  /**
   * Search properties with filters
   */
  async searchProperties(params: PropertySearchParams): Promise<PagedResultDto<PropertyDto>> {
    // Create a new URLSearchParams object
    const searchParams = new URLSearchParams();
    
    // Add page and pageSize parameters
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
    
    // Handle array parameters correctly for ASP.NET Core
    if (params.postType && params.postType.length > 0) {
      params.postType.forEach(type => {
        searchParams.append('postType', type);
      });
    }
    
    if (params.propertyType && params.propertyType.length > 0) {
      params.propertyType.forEach(type => {
        searchParams.append('propertyType', type);
      });
    }
    
    if (params.status && params.status.length > 0) {
      params.status.forEach(status => {
        searchParams.append('status', status);
      });
    }
    
    // Make the request with the formatted query string
    const response = await axiosInstance.get<PagedResultDto<PropertyDto>>(`Property/search?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get count of properties matching search criteria
   */
  async getSearchCount(params: PropertySearchParams): Promise<number> {
    // Create a new URLSearchParams object
    const searchParams = new URLSearchParams();
    
    // Handle array parameters correctly for ASP.NET Core
    if (params.postType && params.postType.length > 0) {
      params.postType.forEach(type => {
        searchParams.append('postType', type);
      });
    }
    
    if (params.propertyType && params.propertyType.length > 0) {
      params.propertyType.forEach(type => {
        searchParams.append('propertyType', type);
      });
    }
    
    if (params.status && params.status.length > 0) {
      params.status.forEach(status => {
        searchParams.append('status', status);
      });
    }
    
    // Make the request with the formatted query string
    const response = await axiosInstance.get<number>(`Property/search/count?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get property by ID
   */
  async getPropertyById(id: string): Promise<PropertyDto> {
    const response = await axiosInstance.get<PropertyDto>(`Property/${id}`);
    return response.data;
  }

  /**
   * Get property history by ID
   */
  async getPropertyHistoryById(id: string): Promise<PropertyHistoryDto[]> {
    const response = await axiosInstance.get<PropertyHistoryDto[]>(`Property/${id}/history`);
    return response.data;
  }

  /**
   * Delete property by ID
   */
  async deleteProperty(id: string): Promise<void> {
    await axiosInstance.delete(`Property/${id}`);
  }

  /**
   * Delete multiple properties
   */
  async deleteBulkProperties(ids: string[]): Promise<void> {
    await axiosInstance.delete('Property/bulk', {
      data: { propertyIds: ids } as BulkPropertyIdsDto,
    });
  }

  /**
   * Update property status
   */
  async updatePropertyStatus(propertyId: string, data: UpdateStatusDto): Promise<void> {
    await axiosInstance.put(`Property/${propertyId}/status`, data);
  }

  /**
   * Update multiple property statuses
   */
  async updateBulkPropertyStatus(data: BulkUpdateStatusDto): Promise<void> {
    await axiosInstance.put('Property/bulk/status', data);
  }
}

export default new PropertyService(); 