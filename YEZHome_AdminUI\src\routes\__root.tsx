import { Outlet, createRootRouteWithContext, redirect, useLocation } from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';
import TanStackQueryLayout from '@/integrations/tanstack-query/layout';
import AdminLayout from '@/components/AdminLayout';
import { isAuthenticated, requireAuth } from '@/lib/auth';
import { Toaster } from '@/components/ui/toaster';

export interface MyRouterContext {
  // Add your router context here
  getTitle?: () => string;
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  beforeLoad: async ({ location }) => {
    // Allow access to login page without authentication
    if (location.pathname === '/login') {
      return
    }

    requireAuth();      

    // If user is authenticated and trying to access login page, redirect to home
    if (isAuthenticated() && location.pathname === '/login') {
      throw redirect({
        to: '/',
      })
    }
    
    return {
      getTitle: () => 'Trang chủ'
    };
  },
  component: () => {
    // Use location from TanStack Router instead of window.location
    const location = useLocation();
    const isLoginPage = location.pathname === '/login';
    
    return (
      <>
        {!isLoginPage ? (
          <AdminLayout>
            <Outlet />
          </AdminLayout>
        ) : (
          <Outlet />
        )}
        <Toaster />
        <TanStackRouterDevtools />
        <TanStackQueryLayout />
      </>
    );
  },
})
