import { PropertyStatus, PropertyType, PostType } from '@/lib/enum';

export interface PropertyCountStats {
  totalProperties: number;
  propertiesByStatus: Record<string, number>;
}

export interface PropertyDto {
  id: string;
  name: string;
  slug?: string;
  propertyType?: PropertyType | string;
  postType?: PostType | string;
  cityId?: number;
  districtId?: number;
  streetId?: number;
  wardId?: number;
  address?: string;
  area?: number;
  price: number;
  videoUrl?: string;
  floors?: number;
  rooms?: number;
  toilets?: number;
  direction?: string;
  balconyDirection?: string;
  legality?: string;
  interior?: string;
  width?: number;
  roadWidth?: number;
  description?: string;
  overview?: string;
  placeData?: string;
  policies?: string;
  neighborhood?: string;
  status?: PropertyStatus | string;
  postPrice: number;
  isHighlighted: boolean;
  isAutoRenew: boolean;
  expiresAt: string;
  updateRemainingTimes?: number;
  propertyMedia?: PropertyMediaDto[];
  propertyReviews?: PropertyReviewDto[];
  ownerId: string;
  owner?: UserDto;
  createdAt: string;
  swLat?: number;
  swLng?: number;
  neLat?: number;
  neLng?: number;
  latitude?: number;
  longitude?: number;
}

export interface PropertyMediaDto {
  id: string;
  propertyID?: string;
  mediaType?: string;
  mediaURL?: string;
  thumbnailURL?: string;
  smallURL?: string;
  mediumURL?: string;
  largeURL?: string;
  filePath?: string;
  uploadedAt: string;
  caption?: string;
  isAvatar: boolean;
}

export interface PropertyReviewDto {
  id: string;
  rating: number;
  reviewText?: string;
  buyerId: string;
  propertyId: string;
}

export interface UserDto {
  id: string;
  fullName?: string;
  email?: string;
  phone?: string;
  memberRank?: string;
}

export interface PagedResultDto<T> {
  items: T[];
  totalCount: number;
  pageCount: number;
  currentPage: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface PropertySearchParams {
  postType?: string[];
  propertyType?: string[];
  status?: string[];
  page?: number;
  pageSize?: number;
}

export interface UpdateStatusDto {
  status: string;
  comment?: string;
}

export interface BulkPropertyIdsDto {
  propertyIds: string[];
}

export interface BulkUpdateStatusDto {
  propertyIds: string[];
  status: string;
  comment?: string;
}

export interface PropertyHistoryDto {
  status: PropertyStatus | string;
  changedAt: string;
  comment: string;
  propertyId: string;
}