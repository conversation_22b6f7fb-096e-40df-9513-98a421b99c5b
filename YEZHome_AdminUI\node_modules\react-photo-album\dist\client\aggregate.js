"use client";
import { jsx } from "react/jsx-runtime";
import { forwardRef } from "react";
import RowsPhotoAlbum from "./rows.js";
import ColumnsPhotoAlbum from "./columns.js";
import MasonryPhotoAlbum from "./masonry.js";
function PhotoAlbum({ layout, ...rest }, ref) {
  if (layout === "rows") return jsx(RowsPhotoAlbum, { ref, ...rest });
  if (layout === "columns") return jsx(ColumnsPhotoAlbum, { ref, ...rest });
  if (layout === "masonry") return jsx(MasonryPhotoAlbum, { ref, ...rest });
  return null;
}
const PhotoAlbum$1 = forwardRef(PhotoAlbum);
export {
  PhotoAlbum$1 as default
};
