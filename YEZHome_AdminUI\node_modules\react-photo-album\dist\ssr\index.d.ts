import React from 'react';
import { CommonPhotoAlbumProps } from '../types.js';

/** SSR component props. */
type SSRProps = {
    /** Photo album layout breakpoints. */
    breakpoints: number[];
    /** Photo album instance, which must be the only child. */
    children: React.ReactElement<Pick<CommonPhotoAlbumProps, "breakpoints" | "defaultContainerWidth">>;
    /** If `true`, do not include the inline stylesheet. */
    unstyled?: boolean;
    /** Custom class names for the container and the breakpoint intervals. */
    classNames?: {
        /** Custom container class name. */
        container?: string;
        /** Custom class names for the breakpoint intervals. */
        breakpoints?: Record<number, string>;
    };
};
declare const _default: React.ForwardRefExoticComponent<SSRProps & React.RefAttributes<HTMLElement>>;

export { _default as default };
export type { SSRProps };
