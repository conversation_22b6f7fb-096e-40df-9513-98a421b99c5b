{"version": 3, "sources": ["../../web-vitals/dist/web-vitals.js"], "sourcesContent": ["var e,n,t,r,i,o=-1,a=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(o=n.timeStamp,e(n))}),!0)},c=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),r=\"navigate\";o>=0?r=\"back-forward-cache\":t&&(document.prerendering||u()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":t.type&&(r=t.type.replace(/_/g,\"-\")));return{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},t||{})),r}}catch(e){}},d=function(e,n,t,r){var i,o;return function(a){n.value>=0&&(a||r)&&((o=n.value-(i||0))||void 0===i)&&(i=n.value,n.delta=o,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&e()}))},v=function(e){var n=!1;return function(){n||(e(),n=!0)}},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),a((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},C=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},b=[1800,3e3],S=function(e,n){n=n||{},C((function(){var t,r=E(),i=f(\"FCP\"),o=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-u(),0),i.entries.push(e),t(!0)))}))}));o&&(t=d(e,i,b,n.reportAllChanges),a((function(r){i=f(\"FCP\"),t=d(e,i,b,n.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,t(!0)}))})))}))},L=[.1,.25],w=function(e,n){n=n||{},S(v((function(){var t,r=f(\"CLS\",0),i=0,o=[],c=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=o[0],t=o[o.length-1];i&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}})),i>r.value&&(r.value=i,r.entries=o,t())},u=s(\"layout-shift\",c);u&&(t=d(e,r,L,n.reportAllChanges),p((function(){c(u.takeRecords()),t(!0)})),a((function(){i=0,r=f(\"CLS\",0),t=d(e,r,L,n.reportAllChanges),l((function(){return t()}))})),setTimeout(t,0))})))},A=0,I=1/0,P=0,M=function(e){e.forEach((function(e){e.interactionId&&(I=Math.min(I,e.interactionId),P=Math.max(P,e.interactionId),A=P?(P-I)/7+1:0)}))},k=function(){return e?A:performance.interactionCount||0},F=function(){\"interactionCount\"in performance||e||(e=s(\"event\",M,{type:\"event\",buffered:!0,durationThreshold:0}))},D=[],x=new Map,R=0,B=function(){var e=Math.min(D.length-1,Math.floor((k()-R)/50));return D[e]},H=[],q=function(e){if(H.forEach((function(n){return n(e)})),e.interactionId||\"first-input\"===e.entryType){var n=D[D.length-1],t=x.get(e.interactionId);if(t||D.length<10||e.duration>n.latency){if(t)e.duration>t.latency?(t.entries=[e],t.latency=e.duration):e.duration===t.latency&&e.startTime===t.entries[0].startTime&&t.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};x.set(r.id,r),D.push(r)}D.sort((function(e,n){return n.latency-e.latency})),D.length>10&&D.splice(10).forEach((function(e){return x.delete(e.id)}))}}},O=function(e){var n=self.requestIdleCallback||self.setTimeout,t=-1;return e=v(e),\"hidden\"===document.visibilityState?e():(t=n(e),p(e)),t},N=[200,500],j=function(e,n){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(n=n||{},C((function(){var t;F();var r,i=f(\"INP\"),o=function(e){O((function(){e.forEach(q);var n=B();n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())}))},c=s(\"event\",o,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});r=d(e,i,N,n.reportAllChanges),c&&(c.observe({type:\"first-input\",buffered:!0}),p((function(){o(c.takeRecords()),r(!0)})),a((function(){R=k(),D.length=0,x.clear(),i=f(\"INP\"),r=d(e,i,N,n.reportAllChanges)})))})))},_=[2500,4e3],z={},G=function(e,n){n=n||{},C((function(){var t,r=E(),i=f(\"LCP\"),o=function(e){n.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-u(),0),i.entries=[e],t())}))},c=s(\"largest-contentful-paint\",o);if(c){t=d(e,i,_,n.reportAllChanges);var m=v((function(){z[i.id]||(o(c.takeRecords()),c.disconnect(),z[i.id]=!0,t(!0))}));[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,(function(){return O(m)}),{once:!0,capture:!0})})),p(m),a((function(r){i=f(\"LCP\"),t=d(e,i,_,n.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,z[i.id]=!0,t(!0)}))}))}}))},J=[800,1800],K=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},Q=function(e,n){n=n||{};var t=f(\"TTFB\"),r=d(e,t,J,n.reportAllChanges);K((function(){var i=c();i&&(t.value=Math.max(i.responseStart-u(),0),t.entries=[i],r(!0),a((function(){t=f(\"TTFB\",0),(r=d(e,t,J,n.reportAllChanges))(!0)})))}))},U={passive:!0,capture:!0},V=new Date,W=function(e,i){n||(n=i,t=e,r=new Date,Z(removeEventListener),X())},X=function(){if(t>=0&&t<r-V){var e={entryType:\"first-input\",name:n.type,target:n.target,cancelable:n.cancelable,startTime:n.timeStamp,processingStart:n.timeStamp+t};i.forEach((function(n){n(e)})),i=[]}},Y=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){W(e,n),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",t,U),removeEventListener(\"pointercancel\",r,U)};addEventListener(\"pointerup\",t,U),addEventListener(\"pointercancel\",r,U)}(n,e):W(n,e)}},Z=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,Y,U)}))},$=[100,300],ee=function(e,r){r=r||{},C((function(){var o,c=E(),u=f(\"FID\"),l=function(e){e.startTime<c.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),o(!0))},m=function(e){e.forEach(l)},h=s(\"first-input\",m);o=d(e,u,$,r.reportAllChanges),h&&(p(v((function(){m(h.takeRecords()),h.disconnect()}))),a((function(){var a;u=f(\"FID\"),o=d(e,u,$,r.reportAllChanges),i=[],t=-1,n=null,Z(addEventListener),a=l,i.push(a),X()})))}))};export{L as CLSThresholds,b as FCPThresholds,$ as FIDThresholds,N as INPThresholds,_ as LCPThresholds,J as TTFBThresholds,w as onCLS,S as onFCP,ee as onFID,j as onINP,G as onLCP,Q as onTTFB};\n"], "mappings": ";;;AAAA,IAAI;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAU;AAAV,IAAY;AAAZ,IAAc,IAAE;AAAhB,IAAmB,IAAE,SAASA,IAAE;AAAC,mBAAiB,YAAY,SAASC,IAAE;AAAC,IAAAA,GAAE,cAAY,IAAEA,GAAE,WAAUD,GAAEC,EAAC;AAAA,EAAE,GAAG,IAAE;AAAC;AAAjH,IAAmH,IAAE,WAAU;AAAC,MAAID,KAAE,KAAK,eAAa,YAAY,oBAAkB,YAAY,iBAAiB,YAAY,EAAE,CAAC;AAAE,MAAGA,MAAGA,GAAE,gBAAc,KAAGA,GAAE,gBAAc,YAAY,IAAI,EAAE,QAAOA;AAAC;AAAvS,IAAyS,IAAE,WAAU;AAAC,MAAIA,KAAE,EAAE;AAAE,SAAOA,MAAGA,GAAE,mBAAiB;AAAC;AAA9V,IAAgW,IAAE,SAASA,IAAEC,IAAE;AAAC,MAAIC,KAAE,EAAE,GAAEC,KAAE;AAAW,OAAG,IAAEA,KAAE,uBAAqBD,OAAI,SAAS,gBAAc,EAAE,IAAE,IAAEC,KAAE,cAAY,SAAS,eAAaA,KAAE,YAAUD,GAAE,SAAOC,KAAED,GAAE,KAAK,QAAQ,MAAK,GAAG;AAAI,SAAM,EAAC,MAAKF,IAAE,OAAM,WAASC,KAAE,KAAGA,IAAE,QAAO,QAAO,OAAM,GAAE,SAAQ,CAAC,GAAE,IAAG,MAAM,OAAO,KAAK,IAAI,GAAE,GAAG,EAAE,OAAO,KAAK,MAAM,gBAAc,KAAK,OAAO,CAAC,IAAE,IAAI,GAAE,gBAAeE,GAAC;AAAC;AAArsB,IAAusB,IAAE,SAASH,IAAEC,IAAEC,IAAE;AAAC,MAAG;AAAC,QAAG,oBAAoB,oBAAoB,SAASF,EAAC,GAAE;AAAC,UAAIG,KAAE,IAAI,oBAAqB,SAASH,IAAE;AAAC,gBAAQ,QAAQ,EAAE,KAAM,WAAU;AAAC,UAAAC,GAAED,GAAE,WAAW,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAE,aAAOG,GAAE,QAAQ,OAAO,OAAO,EAAC,MAAKH,IAAE,UAAS,KAAE,GAAEE,MAAG,CAAC,CAAC,CAAC,GAAEC;AAAA,IAAC;AAAA,EAAC,SAAOH,IAAE;AAAA,EAAC;AAAC;AAAp8B,IAAs8B,IAAE,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,SAAO,SAASC,IAAE;AAAC,IAAAL,GAAE,SAAO,MAAIK,MAAGH,SAAME,KAAEJ,GAAE,SAAOG,MAAG,OAAK,WAASA,QAAKA,KAAEH,GAAE,OAAMA,GAAE,QAAMI,IAAEJ,GAAE,SAAO,SAASD,IAAEC,IAAE;AAAC,aAAOD,KAAEC,GAAE,CAAC,IAAE,SAAOD,KAAEC,GAAE,CAAC,IAAE,sBAAoB;AAAA,IAAM,EAAEA,GAAE,OAAMC,EAAC,GAAEF,GAAEC,EAAC;AAAA,EAAE;AAAC;AAAhqC,IAAkqC,IAAE,SAASD,IAAE;AAAC,wBAAuB,WAAU;AAAC,WAAO,sBAAuB,WAAU;AAAC,aAAOA,GAAE;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAC;AAA3wC,IAA6wC,IAAE,SAASA,IAAE;AAAC,WAAS,iBAAiB,oBAAoB,WAAU;AAAC,iBAAW,SAAS,mBAAiBA,GAAE;AAAA,EAAC,CAAE;AAAC;AAA/3C,IAAi4C,IAAE,SAASA,IAAE;AAAC,MAAIC,KAAE;AAAG,SAAO,WAAU;AAAC,IAAAA,OAAID,GAAE,GAAEC,KAAE;AAAA,EAAG;AAAC;AAAx7C,IAA07C,IAAE;AAA57C,IAA+7C,IAAE,WAAU;AAAC,SAAM,aAAW,SAAS,mBAAiB,SAAS,eAAa,IAAE,IAAE;AAAC;AAAlhD,IAAohD,IAAE,SAASD,IAAE;AAAC,eAAW,SAAS,mBAAiB,IAAE,OAAK,IAAE,uBAAqBA,GAAE,OAAKA,GAAE,YAAU,GAAE,EAAE;AAAE;AAA9nD,IAAgoD,IAAE,WAAU;AAAC,mBAAiB,oBAAmB,GAAE,IAAE,GAAE,iBAAiB,sBAAqB,GAAE,IAAE;AAAC;AAAluD,IAAouD,IAAE,WAAU;AAAC,sBAAoB,oBAAmB,GAAE,IAAE,GAAE,oBAAoB,sBAAqB,GAAE,IAAE;AAAC;AAA50D,IAA80D,IAAE,WAAU;AAAC,SAAO,IAAE,MAAI,IAAE,EAAE,GAAE,EAAE,GAAE,EAAG,WAAU;AAAC,eAAY,WAAU;AAAC,UAAE,EAAE,GAAE,EAAE;AAAA,IAAC,GAAG,CAAC;AAAA,EAAC,CAAE,IAAG,EAAC,IAAI,kBAAiB;AAAC,WAAO;AAAA,EAAC,EAAC;AAAC;AAA38D,IAA68D,IAAE,SAASA,IAAE;AAAC,WAAS,eAAa,iBAAiB,sBAAsB,WAAU;AAAC,WAAOA,GAAE;AAAA,EAAC,GAAG,IAAE,IAAEA,GAAE;AAAC;AAAvjE,IAAyjE,IAAE,CAAC,MAAK,GAAG;AAApkE,IAAskE,IAAE,SAASA,IAAEC,IAAE;AAAC,EAAAA,KAAEA,MAAG,CAAC,GAAE,EAAG,WAAU;AAAC,QAAIC,IAAEC,KAAE,EAAE,GAAEC,KAAE,EAAE,KAAK,GAAEC,KAAE,EAAE,SAAS,SAASL,IAAE;AAAC,MAAAA,GAAE,QAAS,SAASA,IAAE;AAAC,qCAA2BA,GAAE,SAAOK,GAAE,WAAW,GAAEL,GAAE,YAAUG,GAAE,oBAAkBC,GAAE,QAAM,KAAK,IAAIJ,GAAE,YAAU,EAAE,GAAE,CAAC,GAAEI,GAAE,QAAQ,KAAKJ,EAAC,GAAEE,GAAE,IAAE;AAAA,MAAG,CAAE;AAAA,IAAC,CAAE;AAAE,IAAAG,OAAIH,KAAE,EAAEF,IAAEI,IAAE,GAAEH,GAAE,gBAAgB,GAAE,EAAG,SAASE,IAAE;AAAC,MAAAC,KAAE,EAAE,KAAK,GAAEF,KAAE,EAAEF,IAAEI,IAAE,GAAEH,GAAE,gBAAgB,GAAE,EAAG,WAAU;AAAC,QAAAG,GAAE,QAAM,YAAY,IAAI,IAAED,GAAE,WAAUD,GAAE,IAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE;AAAA,EAAE,CAAE;AAAC;AAAv+E,IAAy+E,IAAE,CAAC,KAAG,IAAG;AAAl/E,IAAo/E,IAAE,SAASF,IAAEC,IAAE;AAAC,EAAAA,KAAEA,MAAG,CAAC,GAAE,EAAE,EAAG,WAAU;AAAC,QAAIC,IAAEC,KAAE,EAAE,OAAM,CAAC,GAAEC,KAAE,GAAEC,KAAE,CAAC,GAAEE,KAAE,SAASP,IAAE;AAAC,MAAAA,GAAE,QAAS,SAASA,IAAE;AAAC,YAAG,CAACA,GAAE,gBAAe;AAAC,cAAIC,KAAEI,GAAE,CAAC,GAAEH,KAAEG,GAAEA,GAAE,SAAO,CAAC;AAAE,UAAAD,MAAGJ,GAAE,YAAUE,GAAE,YAAU,OAAKF,GAAE,YAAUC,GAAE,YAAU,OAAKG,MAAGJ,GAAE,OAAMK,GAAE,KAAKL,EAAC,MAAII,KAAEJ,GAAE,OAAMK,KAAE,CAACL,EAAC;AAAA,QAAE;AAAA,MAAC,CAAE,GAAEI,KAAED,GAAE,UAAQA,GAAE,QAAMC,IAAED,GAAE,UAAQE,IAAEH,GAAE;AAAA,IAAE,GAAEM,KAAE,EAAE,gBAAeD,EAAC;AAAE,IAAAC,OAAIN,KAAE,EAAEF,IAAEG,IAAE,GAAEF,GAAE,gBAAgB,GAAE,EAAG,WAAU;AAAC,MAAAM,GAAEC,GAAE,YAAY,CAAC,GAAEN,GAAE,IAAE;AAAA,IAAC,CAAE,GAAE,EAAG,WAAU;AAAC,MAAAE,KAAE,GAAED,KAAE,EAAE,OAAM,CAAC,GAAED,KAAE,EAAEF,IAAEG,IAAE,GAAEF,GAAE,gBAAgB,GAAE,EAAG,WAAU;AAAC,eAAOC,GAAE;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,GAAE,WAAWA,IAAE,CAAC;AAAA,EAAE,CAAE,CAAC;AAAC;AAAj/F,IAAm/F,IAAE;AAAr/F,IAAu/F,IAAE,IAAE;AAA3/F,IAA6/F,IAAE;AAA//F,IAAigG,IAAE,SAASF,IAAE;AAAC,EAAAA,GAAE,QAAS,SAASA,IAAE;AAAC,IAAAA,GAAE,kBAAgB,IAAE,KAAK,IAAI,GAAEA,GAAE,aAAa,GAAE,IAAE,KAAK,IAAI,GAAEA,GAAE,aAAa,GAAE,IAAE,KAAG,IAAE,KAAG,IAAE,IAAE;AAAA,EAAE,CAAE;AAAC;AAAvoG,IAAyoG,IAAE,WAAU;AAAC,SAAO,IAAE,IAAE,YAAY,oBAAkB;AAAC;AAAhsG,IAAksG,IAAE,WAAU;AAAC,wBAAqB,eAAa,MAAI,IAAE,EAAE,SAAQ,GAAE,EAAC,MAAK,SAAQ,UAAS,MAAG,mBAAkB,EAAC,CAAC;AAAE;AAAnzG,IAAqzG,IAAE,CAAC;AAAxzG,IAA0zG,IAAE,oBAAI;AAAh0G,IAAo0G,IAAE;AAAt0G,IAAw0G,IAAE,WAAU;AAAC,MAAIA,KAAE,KAAK,IAAI,EAAE,SAAO,GAAE,KAAK,OAAO,EAAE,IAAE,KAAG,EAAE,CAAC;AAAE,SAAO,EAAEA,EAAC;AAAC;AAAl5G,IAAo5G,IAAE,CAAC;AAAv5G,IAAy5G,IAAE,SAASA,IAAE;AAAC,MAAG,EAAE,QAAS,SAASC,IAAE;AAAC,WAAOA,GAAED,EAAC;AAAA,EAAC,CAAE,GAAEA,GAAE,iBAAe,kBAAgBA,GAAE,WAAU;AAAC,QAAIC,KAAE,EAAE,EAAE,SAAO,CAAC,GAAEC,KAAE,EAAE,IAAIF,GAAE,aAAa;AAAE,QAAGE,MAAG,EAAE,SAAO,MAAIF,GAAE,WAASC,GAAE,SAAQ;AAAC,UAAGC,GAAE,CAAAF,GAAE,WAASE,GAAE,WAASA,GAAE,UAAQ,CAACF,EAAC,GAAEE,GAAE,UAAQF,GAAE,YAAUA,GAAE,aAAWE,GAAE,WAASF,GAAE,cAAYE,GAAE,QAAQ,CAAC,EAAE,aAAWA,GAAE,QAAQ,KAAKF,EAAC;AAAA,WAAM;AAAC,YAAIG,KAAE,EAAC,IAAGH,GAAE,eAAc,SAAQA,GAAE,UAAS,SAAQ,CAACA,EAAC,EAAC;AAAE,UAAE,IAAIG,GAAE,IAAGA,EAAC,GAAE,EAAE,KAAKA,EAAC;AAAA,MAAC;AAAC,QAAE,KAAM,SAASH,IAAEC,IAAE;AAAC,eAAOA,GAAE,UAAQD,GAAE;AAAA,MAAO,CAAE,GAAE,EAAE,SAAO,MAAI,EAAE,OAAO,EAAE,EAAE,QAAS,SAASA,IAAE;AAAC,eAAO,EAAE,OAAOA,GAAE,EAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAv7H,IAAy7H,IAAE,SAASA,IAAE;AAAC,MAAIC,KAAE,KAAK,uBAAqB,KAAK,YAAWC,KAAE;AAAG,SAAOF,KAAE,EAAEA,EAAC,GAAE,aAAW,SAAS,kBAAgBA,GAAE,KAAGE,KAAED,GAAED,EAAC,GAAE,EAAEA,EAAC,IAAGE;AAAC;AAAjkI,IAAmkI,IAAE,CAAC,KAAI,GAAG;AAA7kI,IAA+kI,IAAE,SAASF,IAAEC,IAAE;AAAC,8BAA2B,QAAM,mBAAkB,uBAAuB,cAAYA,KAAEA,MAAG,CAAC,GAAE,EAAG,WAAU;AAAC,QAAIC;AAAE,MAAE;AAAE,QAAIC,IAAEC,KAAE,EAAE,KAAK,GAAEC,KAAE,SAASL,IAAE;AAAC,QAAG,WAAU;AAAC,QAAAA,GAAE,QAAQ,CAAC;AAAE,YAAIC,KAAE,EAAE;AAAE,QAAAA,MAAGA,GAAE,YAAUG,GAAE,UAAQA,GAAE,QAAMH,GAAE,SAAQG,GAAE,UAAQH,GAAE,SAAQE,GAAE;AAAA,MAAE,CAAE;AAAA,IAAC,GAAEI,KAAE,EAAE,SAAQF,IAAE,EAAC,mBAAkB,UAAQH,KAAED,GAAE,sBAAoB,WAASC,KAAEA,KAAE,GAAE,CAAC;AAAE,IAAAC,KAAE,EAAEH,IAAEI,IAAE,GAAEH,GAAE,gBAAgB,GAAEM,OAAIA,GAAE,QAAQ,EAAC,MAAK,eAAc,UAAS,KAAE,CAAC,GAAE,EAAG,WAAU;AAAC,MAAAF,GAAEE,GAAE,YAAY,CAAC,GAAEJ,GAAE,IAAE;AAAA,IAAC,CAAE,GAAE,EAAG,WAAU;AAAC,UAAE,EAAE,GAAE,EAAE,SAAO,GAAE,EAAE,MAAM,GAAEC,KAAE,EAAE,KAAK,GAAED,KAAE,EAAEH,IAAEI,IAAE,GAAEH,GAAE,gBAAgB;AAAA,IAAC,CAAE;AAAA,EAAE,CAAE;AAAE;AAAroJ,IAAuoJ,IAAE,CAAC,MAAK,GAAG;AAAlpJ,IAAopJ,IAAE,CAAC;AAAvpJ,IAAypJ,IAAE,SAASD,IAAEC,IAAE;AAAC,EAAAA,KAAEA,MAAG,CAAC,GAAE,EAAG,WAAU;AAAC,QAAIC,IAAEC,KAAE,EAAE,GAAEC,KAAE,EAAE,KAAK,GAAEC,KAAE,SAASL,IAAE;AAAC,MAAAC,GAAE,qBAAmBD,KAAEA,GAAE,MAAM,EAAE,IAAGA,GAAE,QAAS,SAASA,IAAE;AAAC,QAAAA,GAAE,YAAUG,GAAE,oBAAkBC,GAAE,QAAM,KAAK,IAAIJ,GAAE,YAAU,EAAE,GAAE,CAAC,GAAEI,GAAE,UAAQ,CAACJ,EAAC,GAAEE,GAAE;AAAA,MAAE,CAAE;AAAA,IAAC,GAAEK,KAAE,EAAE,4BAA2BF,EAAC;AAAE,QAAGE,IAAE;AAAC,MAAAL,KAAE,EAAEF,IAAEI,IAAE,GAAEH,GAAE,gBAAgB;AAAE,UAAIQ,KAAE,EAAG,WAAU;AAAC,UAAEL,GAAE,EAAE,MAAIC,GAAEE,GAAE,YAAY,CAAC,GAAEA,GAAE,WAAW,GAAE,EAAEH,GAAE,EAAE,IAAE,MAAGF,GAAE,IAAE;AAAA,MAAE,CAAE;AAAE,OAAC,WAAU,OAAO,EAAE,QAAS,SAASF,IAAE;AAAC,yBAAiBA,IAAG,WAAU;AAAC,iBAAO,EAAES,EAAC;AAAA,QAAC,GAAG,EAAC,MAAK,MAAG,SAAQ,KAAE,CAAC;AAAA,MAAC,CAAE,GAAE,EAAEA,EAAC,GAAE,EAAG,SAASN,IAAE;AAAC,QAAAC,KAAE,EAAE,KAAK,GAAEF,KAAE,EAAEF,IAAEI,IAAE,GAAEH,GAAE,gBAAgB,GAAE,EAAG,WAAU;AAAC,UAAAG,GAAE,QAAM,YAAY,IAAI,IAAED,GAAE,WAAU,EAAEC,GAAE,EAAE,IAAE,MAAGF,GAAE,IAAE;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE;AAAA,IAAC;AAAA,EAAC,CAAE;AAAC;AAA/wK,IAAixK,IAAE,CAAC,KAAI,IAAI;AAA5xK,IAA8xK,IAAE,SAASF,GAAEC,IAAE;AAAC,WAAS,eAAa,EAAG,WAAU;AAAC,WAAOD,GAAEC,EAAC;AAAA,EAAC,CAAE,IAAE,eAAa,SAAS,aAAW,iBAAiB,QAAQ,WAAU;AAAC,WAAOD,GAAEC,EAAC;AAAA,EAAC,GAAG,IAAE,IAAE,WAAWA,IAAE,CAAC;AAAC;AAAv8K,IAAy8K,IAAE,SAASD,IAAEC,IAAE;AAAC,EAAAA,KAAEA,MAAG,CAAC;AAAE,MAAIC,KAAE,EAAE,MAAM,GAAEC,KAAE,EAAEH,IAAEE,IAAE,GAAED,GAAE,gBAAgB;AAAE,IAAG,WAAU;AAAC,QAAIG,KAAE,EAAE;AAAE,IAAAA,OAAIF,GAAE,QAAM,KAAK,IAAIE,GAAE,gBAAc,EAAE,GAAE,CAAC,GAAEF,GAAE,UAAQ,CAACE,EAAC,GAAED,GAAE,IAAE,GAAE,EAAG,WAAU;AAAC,MAAAD,KAAE,EAAE,QAAO,CAAC,IAAGC,KAAE,EAAEH,IAAEE,IAAE,GAAED,GAAE,gBAAgB,GAAG,IAAE;AAAA,IAAC,CAAE;AAAA,EAAE,CAAE;AAAC;AAA7qL,IAA+qL,IAAE,EAAC,SAAQ,MAAG,SAAQ,KAAE;AAAvsL,IAAysL,IAAE,oBAAI;AAA/sL,IAAotL,IAAE,SAASD,IAAEI,IAAE;AAAC,QAAI,IAAEA,IAAE,IAAEJ,IAAE,IAAE,oBAAI,QAAK,EAAE,mBAAmB,GAAE,EAAE;AAAE;AAAtxL,IAAwxL,IAAE,WAAU;AAAC,MAAG,KAAG,KAAG,IAAE,IAAE,GAAE;AAAC,QAAIA,KAAE,EAAC,WAAU,eAAc,MAAK,EAAE,MAAK,QAAO,EAAE,QAAO,YAAW,EAAE,YAAW,WAAU,EAAE,WAAU,iBAAgB,EAAE,YAAU,EAAC;AAAE,MAAE,QAAS,SAASC,IAAE;AAAC,MAAAA,GAAED,EAAC;AAAA,IAAC,CAAE,GAAE,IAAE,CAAC;AAAA,EAAC;AAAC;AAAj+L,IAAm+L,IAAE,SAASA,IAAE;AAAC,MAAGA,GAAE,YAAW;AAAC,QAAIC,MAAGD,GAAE,YAAU,OAAK,oBAAI,SAAK,YAAY,IAAI,KAAGA,GAAE;AAAU,qBAAeA,GAAE,OAAK,SAASA,IAAEC,IAAE;AAAC,UAAIC,KAAE,WAAU;AAAC,UAAEF,IAAEC,EAAC,GAAEG,GAAE;AAAA,MAAC,GAAED,KAAE,WAAU;AAAC,QAAAC,GAAE;AAAA,MAAC,GAAEA,KAAE,WAAU;AAAC,4BAAoB,aAAYF,IAAE,CAAC,GAAE,oBAAoB,iBAAgBC,IAAE,CAAC;AAAA,MAAC;AAAE,uBAAiB,aAAYD,IAAE,CAAC,GAAE,iBAAiB,iBAAgBC,IAAE,CAAC;AAAA,IAAC,EAAEF,IAAED,EAAC,IAAE,EAAEC,IAAED,EAAC;AAAA,EAAC;AAAC;AAAt0M,IAAw0M,IAAE,SAASA,IAAE;AAAC,GAAC,aAAY,WAAU,cAAa,aAAa,EAAE,QAAS,SAASC,IAAE;AAAC,WAAOD,GAAEC,IAAE,GAAE,CAAC;AAAA,EAAC,CAAE;AAAC;AAAh7M,IAAk7M,IAAE,CAAC,KAAI,GAAG;AAA57M,IAA87M,KAAG,SAASD,IAAEG,IAAE;AAAC,EAAAA,KAAEA,MAAG,CAAC,GAAE,EAAG,WAAU;AAAC,QAAIE,IAAEE,KAAE,EAAE,GAAEC,KAAE,EAAE,KAAK,GAAEE,KAAE,SAASV,IAAE;AAAC,MAAAA,GAAE,YAAUO,GAAE,oBAAkBC,GAAE,QAAMR,GAAE,kBAAgBA,GAAE,WAAUQ,GAAE,QAAQ,KAAKR,EAAC,GAAEK,GAAE,IAAE;AAAA,IAAE,GAAEI,KAAE,SAAST,IAAE;AAAC,MAAAA,GAAE,QAAQU,EAAC;AAAA,IAAC,GAAEC,KAAE,EAAE,eAAcF,EAAC;AAAE,IAAAJ,KAAE,EAAEL,IAAEQ,IAAE,GAAEL,GAAE,gBAAgB,GAAEQ,OAAI,EAAE,EAAG,WAAU;AAAC,MAAAF,GAAEE,GAAE,YAAY,CAAC,GAAEA,GAAE,WAAW;AAAA,IAAC,CAAE,CAAC,GAAE,EAAG,WAAU;AAAC,UAAIL;AAAE,MAAAE,KAAE,EAAE,KAAK,GAAEH,KAAE,EAAEL,IAAEQ,IAAE,GAAEL,GAAE,gBAAgB,GAAE,IAAE,CAAC,GAAE,IAAE,IAAG,IAAE,MAAK,EAAE,gBAAgB,GAAEG,KAAEI,IAAE,EAAE,KAAKJ,EAAC,GAAE,EAAE;AAAA,IAAC,CAAE;AAAA,EAAE,CAAE;AAAC;", "names": ["e", "n", "t", "r", "i", "o", "a", "c", "u", "m", "l", "h"]}