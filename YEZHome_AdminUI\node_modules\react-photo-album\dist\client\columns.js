"use client";
import { jsx } from "react/jsx-runtime";
import { useMemo, forwardRef } from "react";
import { useContainerWidth } from "./hooks.js";
import StaticPhotoAlbum from "../static/index.js";
import resolveColumnsProps from "./columnsProps.js";
import computeColumnsLayout from "../layouts/columns.js";
function ColumnsPhotoAlbum({ photos, breakpoints, defaultContainerWidth, ...rest }, ref) {
  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints, defaultContainerWidth);
  const { spacing, padding, columns, ...restProps } = resolveColumnsProps(containerWidth, { photos, ...rest });
  const model = useMemo(
    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && columns !== void 0 ? computeColumnsLayout(photos, spacing, padding, containerWidth, columns) : void 0,
    [photos, spacing, padding, containerWidth, columns]
  );
  return jsx(StaticPhotoAlbum, { layout: "columns", ref: containerRef, model, ...restProps });
}
const ColumnsPhotoAlbum$1 = forwardRef(ColumnsPhotoAlbum);
export {
  ColumnsPhotoAlbum$1 as default
};
