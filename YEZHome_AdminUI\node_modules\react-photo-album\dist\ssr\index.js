"use client";
import { jsxs, Fragment, jsx } from "react/jsx-runtime";
import { useState, isValidElement, cloneElement, forwardRef } from "react";
import { clsx } from "../utils/index.js";
import { useContainerWidth } from "../client/hooks.js";
import { useBreakpoints, StyledBreakpoints } from "./breakpoints.js";
function SSR({ breakpoints: breakpointsProp, unstyled, classNames, children }, ref) {
  const { breakpoints, containerClass, breakpointClass } = useBreakpoints("ssr", breakpointsProp);
  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints);
  const [hydratedBreakpoint, setHydratedBreakpoint] = useState();
  if (!Array.isArray(breakpoints) || breakpoints.length === 0 || !isValidElement(children)) return null;
  if (containerWidth !== void 0 && hydratedBreakpoint === void 0) {
    setHydratedBreakpoint(containerWidth);
  }
  return jsxs(Fragment, { children: [
    !unstyled && hydratedBreakpoint === void 0 && jsx(
      StyledBreakpoints,
      {
        breakpoints,
        containerClass,
        breakpointClass
      }
    ),
    jsx("div", { ref: containerRef, className: clsx(containerClass, classNames?.container), children: breakpoints.map(
      (breakpoint) => (hydratedBreakpoint === void 0 || hydratedBreakpoint === breakpoint) && jsx(
        "div",
        {
          className: clsx(breakpointClass(breakpoint), classNames?.breakpoints?.[breakpoint]),
          children: cloneElement(children, { breakpoints, defaultContainerWidth: breakpoint })
        },
        breakpoint
      )
    ) })
  ] });
}
const SSR$1 = forwardRef(SSR);
export {
  SSR$1 as default
};
