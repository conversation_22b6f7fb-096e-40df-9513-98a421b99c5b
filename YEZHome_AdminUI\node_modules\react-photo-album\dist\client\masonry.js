"use client";
import { jsx } from "react/jsx-runtime";
import { useMemo, forwardRef } from "react";
import { useContainerWidth } from "./hooks.js";
import StaticPhotoAlbum from "../static/index.js";
import resolveMasonryProps from "./masonryProps.js";
import computeMasonryLayout from "../layouts/masonry.js";
function MasonryPhotoAlbum({ photos, breakpoints, defaultContainerWidth, ...rest }, ref) {
  const { containerRef, containerWidth } = useContainerWidth(ref, breakpoints, defaultContainerWidth);
  const { spacing, padding, columns, ...restProps } = resolveMasonryProps(containerWidth, { photos, ...rest });
  const model = useMemo(
    () => containerWidth !== void 0 && spacing !== void 0 && padding !== void 0 && columns !== void 0 ? computeMasonryLayout(photos, spacing, padding, containerWidth, columns) : void 0,
    [photos, spacing, padding, containerWidth, columns]
  );
  return jsx(StaticPhotoAlbum, { layout: "masonry", ref: containerRef, model, ...restProps });
}
const MasonryPhotoAlbum$1 = forwardRef(MasonryPhotoAlbum);
export {
  MasonryPhotoAlbum$1 as default
};
