{"name": "react-photo-album", "version": "3.1.0", "description": "Responsive photo gallery component for React", "author": "<PERSON>", "license": "MIT", "type": "module", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./ssr": {"types": "./dist/ssr/index.d.ts", "default": "./dist/ssr/index.js"}, "./scroll": {"types": "./dist/scroll/index.d.ts", "default": "./dist/scroll/index.js"}, "./server": {"types": "./dist/server/index.d.ts", "default": "./dist/server/index.js"}, "./rows.css": {"types": "./dist/styles/rows.css.d.ts", "default": "./dist/styles/rows.css"}, "./columns.css": {"types": "./dist/styles/columns.css.d.ts", "default": "./dist/styles/columns.css"}, "./masonry.css": {"types": "./dist/styles/masonry.css.d.ts", "default": "./dist/styles/masonry.css"}, "./styles.css": {"types": "./dist/styles/styles.css.d.ts", "default": "./dist/styles/styles.css"}}, "typesVersions": {"*": {"*": ["dist/index.d.ts"], "ssr": ["dist/ssr/index.d.ts"], "scroll": ["dist/scroll/index.d.ts"], "server": ["dist/server/index.d.ts"], "rows.css": ["dist/styles/rows.css.d.ts"], "columns.css": ["dist/styles/columns.css.d.ts"], "masonry.css": ["dist/styles/masonry.css.d.ts"], "styles.css": ["dist/styles/styles.css.d.ts"]}}, "files": ["dist"], "sideEffects": ["*.css"], "homepage": "https://react-photo-album.com", "repository": {"type": "git", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>/react-photo-album.git"}, "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>/react-photo-album/issues"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public", "provenance": true}, "peerDependencies": {"@types/react": "^18 || ^19", "react": "^18 || ^19"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "keywords": ["react", "image", "photo", "album", "gallery", "masonry", "photo album", "photo gallery", "image gallery", "photo layout", "image layout", "react gallery", "react photo album", "react photo gallery", "react image gallery", "react-photo-album", "react-photo-gallery"]}