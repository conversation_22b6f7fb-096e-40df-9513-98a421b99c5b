import * as react_jsx_runtime from 'react/jsx-runtime';
import { Photo, LayoutType, NonOptional, RowsPhotoAlbumProps, ColumnsPhotoAlbumProps, MasonryPhotoAlbumProps } from '../types.js';
import 'react';

type RowsServerPhotoAlbumProps<TPhoto extends Photo> = NonOptional<Omit<RowsPhotoAlbumProps<TPhoto>, "defaultContainerWidth" | "onClick" | "skeleton">, "breakpoints">;
type ColumnsServerPhotoAlbumProps<TPhoto extends Photo> = NonOptional<Omit<ColumnsPhotoAlbumProps<TPhoto>, "defaultContainerWidth" | "onClick" | "skeleton">, "breakpoints">;
type MasonryServerPhotoAlbumProps<TPhoto extends Photo> = NonOptional<Omit<MasonryPhotoAlbumProps<TPhoto>, "defaultContainerWidth" | "onClick" | "skeleton">, "breakpoints">;
/** ServerPhotoAlbum component props. */
type ServerPhotoAlbumProps<TPhoto extends Photo> = {
    /** If `true`, do not include the inline stylesheet. */
    unstyled?: boolean;
    /** Custom class names for the container and the breakpoint intervals. */
    classNames?: {
        /** Custom container class name. */
        container?: string;
        /** Custom class names for the breakpoint intervals. */
        breakpoints?: Record<number, string>;
    };
} & (({
    layout: Extract<LayoutType, "rows">;
} & RowsServerPhotoAlbumProps<TPhoto>) | ({
    layout: Extract<LayoutType, "columns">;
} & ColumnsServerPhotoAlbumProps<TPhoto>) | ({
    layout: Extract<LayoutType, "masonry">;
} & MasonryServerPhotoAlbumProps<TPhoto>));
/** ServerPhotoAlbum component. */
declare function ServerPhotoAlbum<TPhoto extends Photo>({ layout, unstyled, classNames, breakpoints: breakpointsProp, ...props }: ServerPhotoAlbumProps<TPhoto>): react_jsx_runtime.JSX.Element | null;

export { ServerPhotoAlbum as default };
export type { ServerPhotoAlbumProps };
