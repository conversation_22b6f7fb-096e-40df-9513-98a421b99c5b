.react-photo-album{display:flex}.react-photo-album,.react-photo-album *{box-sizing:border-box}.react-photo-album--track{align-items:flex-start;display:flex}.react-photo-album--photo{padding:calc(var(--react-photo-album--padding)*1px);position:relative}.react-photo-album--image{aspect-ratio:var(--react-photo-album--photo-width)/var(--react-photo-album--photo-height);display:block;height:auto;width:100%}.react-photo-album--button{background:initial;border:initial;cursor:pointer}.react-photo-album--masonry{justify-content:space-between}.react-photo-album--masonry .react-photo-album--track{flex-direction:column;justify-content:flex-start;row-gap:calc(var(--react-photo-album--spacing)*1px);width:calc((100% - var(--react-photo-album--spacing)*1px*(var(--react-photo-album--columns) - 1))/var(--react-photo-album--columns))}.react-photo-album--masonry .react-photo-album--photo{width:100%}