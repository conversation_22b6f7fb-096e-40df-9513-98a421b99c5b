import { Photo, MasonryPhotoAlbumProps, ComponentsProps, Render, ResponsiveSizes, ClickHandler } from '../types.js';
import 'react';

declare function resolveMasonryProps<TPhoto extends Photo>(containerWidth: number | undefined, { columns, ...rest }: MasonryPhotoAlbumProps<TPhoto>): {
    columns: number | undefined;
    spacing: number | undefined;
    padding: number | undefined;
    componentsProps: ComponentsProps<TPhoto>;
    render: Render<TPhoto> | undefined;
    photos: TPhoto[];
    sizes?: ResponsiveSizes;
    breakpoints?: number[];
    defaultContainerWidth?: number;
    onClick?: ClickHandler<TPhoto> | undefined;
    skeleton?: React.ReactNode;
};

export { resolveMasonryProps as default };
