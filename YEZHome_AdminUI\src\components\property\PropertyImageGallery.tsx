"use client";

import { useState, useMemo } from "react";
import { Expand } from "lucide-react";
import { RowsPhotoAlbum } from "react-photo-album";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import "react-photo-album/rows.css";

// Import lightbox plugins
import Fullscreen from "yet-another-react-lightbox/plugins/fullscreen";
import Slideshow from "yet-another-react-lightbox/plugins/slideshow";
import Zoom from "yet-another-react-lightbox/plugins/zoom";

// Import Dialog components
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function PropertyImageGallery({ images = [], propertyName = "" }: { images: string[], propertyName: string }) {
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(-1);

  // Convert image URLs to the format required by react-photo-album
  const photos = useMemo(() => {
    return images.map((src) => ({
      src,
      width: 1200, // Default width for calculation
      height: 800, // Default height for calculation
      alt: propertyName || "Property image",
    }));
  }, [images, propertyName]);

  // Prepare slides for the lightbox
  const slides = useMemo(() => {
    return images.map((src) => ({
      src,
      alt: propertyName || "Property image",
    }));
  }, [images, propertyName]);

  if (!images || images.length === 0) {
    return (
      <div className="relative w-full h-[400px] bg-gray-200 rounded-lg">
        <div className="absolute inset-0 flex items-center justify-center text-gray-500">
          Không có ảnh
        </div>
      </div>
    );
  }

  // Show up to 8 images in the grid layout
  const displayImages = images.slice(0, Math.min(8, images.length));
  const hasMoreImages = images.length > 8;

  return (
    <>
      <div className="relative">
        {/* Image grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
          {displayImages.map((image, index) => (
            <div 
              key={index} 
              className="relative aspect-square cursor-pointer overflow-hidden rounded-md"
              onClick={() => setLightboxIndex(index)}
            >
              <img
                src={image}
                alt={`${propertyName || "Property"} image ${index + 1}`}
                className="w-full h-full object-cover hover:opacity-90 transition-opacity"
              />
              
              {/* Show "Xem tất cả" button on the last visible image if there are more */}
              {hasMoreImages && index === displayImages.length - 1 && (
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsGalleryOpen(true);
                    }}
                    className="bg-white bg-opacity-90 text-gray-800 p-2 rounded-md flex items-center gap-1 text-sm font-medium"
                  >
                    <Expand className="h-4 w-4" />
                    Xem tất cả {images.length} ảnh
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Gallery Modal */}
      <Dialog open={isGalleryOpen} onOpenChange={setIsGalleryOpen}>
        <DialogContent
          className="max-w-7xl p-6 h-[94vh] w-[90vw] bg-white"
        >
          <DialogTitle className="sr-only">
            {propertyName ? `${propertyName} Images` : "Property Images"}
          </DialogTitle>
          <div className="relative h-full w-full flex flex-col">
            <ScrollArea className="h-[90vh]">
              <RowsPhotoAlbum
                photos={photos}
                defaultContainerWidth={1200}
                sizes={{
                  size: "1168px",
                  sizes: [{ viewport: "(max-width: 1200px)", size: "calc(100vw - 32px)" }],
                }}
                onClick={({ index }) => setLightboxIndex(index)}
              />
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>

      {/* Lightbox for individual image viewing */}
      <Lightbox
        slides={slides}
        open={lightboxIndex >= 0}
        index={lightboxIndex}
        close={() => setLightboxIndex(-1)}
        plugins={[Fullscreen, Slideshow, Zoom]}
      />
    </>
  );
}
